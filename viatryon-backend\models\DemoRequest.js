const mongoose = require('mongoose');

const demoRequestSchema = new mongoose.Schema({
  // Common fields for both demo forms
  name: {
    type: String,
    required: true,
    trim: true
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  company: {
    type: String,
    trim: true
  },
  phone: {
    type: String,
    trim: true
  },
  message: {
    type: String,
    trim: true
  },
  
  // Specific to scheduled demo form
  preferredDate: {
    type: Date
  },
  preferredTime: {
    type: String
  },
  
  // Demo status and management
  status: {
    type: String,
    enum: ['new', 'scheduled', 'completed', 'cancelled', 'no-show'],
    default: 'new'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // Demo details
  scheduledDate: {
    type: Date,
    default: null
  },
  scheduledTime: {
    type: String,
    default: null
  },
  meetingLink: {
    type: String,
    default: null
  },
  duration: {
    type: Number, // in minutes
    default: 30
  },
  
  // Follow-up and notes
  notes: [{
    content: String,
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  followUpDate: {
    type: Date,
    default: null
  },
  
  // Technical details
  ipAddress: {
    type: String,
    default: null
  },
  userAgent: {
    type: String,
    default: null
  },
  source: {
    type: String,
    enum: ['demo-form', 'request-demo', 'schedule-demo'],
    default: 'demo-form'
  },
  
  // Business metrics
  leadScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 50
  },
  expectedRevenue: {
    type: Number,
    default: 0
  },
  industry: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
demoRequestSchema.index({ status: 1, createdAt: -1 });
demoRequestSchema.index({ email: 1 });
demoRequestSchema.index({ scheduledDate: 1 });
demoRequestSchema.index({ createdAt: -1 });
demoRequestSchema.index({ company: 1 });

// Virtual for full name
demoRequestSchema.virtual('fullName').get(function() {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.name || '';
});

// Ensure virtual fields are serialized
demoRequestSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('DemoRequest', demoRequestSchema);
