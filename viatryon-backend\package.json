{"name": "v2-backend", "version": "1.0.0", "description": "Backend for Via Tryon", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@types/whatwg-url": "^11.0.5", "bcryptjs": "^2.4.3", "bson": "^6.10.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "ip-address": "^9.0.5", "jsbn": "^1.1.0", "jsonwebtoken": "^9.0.0", "kareem": "^2.6.3", "mongodb": "^6.16.0", "mongodb-connection-string-url": "^3.0.2", "mongoose": "^8.15.1", "nodemailer": "^7.0.3", "sift": "^17.1.3", "smart-buffer": "^4.2.0", "socks": "^2.8.4", "sprintf-js": "^1.1.3", "tr46": "^5.1.1", "whatwg-url": "^14.2.0"}, "devDependencies": {"nodemon": "^2.0.22"}}