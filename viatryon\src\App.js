import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { CartProvider } from './context/CartContext';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import HowItWorks from './pages/HowItWorks';
import WhyViaTryon from './pages/WhyViaTryon';
import Contact from './pages/Contact';
import SearchResults from './pages/SearchResults';
import DemoForm from './components/DemoForm';
import VirtualTryOn from './pages/VirtualTryOn';
import ProductDetails from './pages/ProductDetails';
import Requirements from './pages/Requirements';
import AdminDashboard from './pages/admin/AdminDashboard';
import ClientDashboard from './pages/client/ClientDashboard';
import Clients from './pages/admin/Clients';
import TryOnAnalytics from './pages/admin/TryOnAnalytics';
import AdminAnalytics from './pages/admin/analytics/AdminAnalytics';
import Requests from './pages/admin/Requests';
import Settings from './pages/admin/Settings';
import ClientAnalytics from './pages/client/analytics/ClientAnalytics';
import ClientSettings from './pages/client/ClientSettings';
import Pricing from './pages/Pricing';
import ProductShowcase from './pages/ProductShowcase';

// Protected Route component
const ProtectedRoute = ({ children, allowedRoles }) => {
  const user = JSON.parse(localStorage.getItem('user'));
  const token = localStorage.getItem('token');
  
  if (!user || !token) {
    return <Navigate to="/login" state={{ from: window.location.pathname }} />;
  }

  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return <Navigate to="/" />;
  }

  return children;
};

const AppContent = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');
  const isClientRoute = location.pathname.startsWith('/client');
  const isVirtualTryOnRoute = location.pathname === '/virtual-try-on';
  const isTryOnRoute = location.pathname.startsWith('/try-on');
  const showNavbarFooter = !isAdminRoute && !isClientRoute && !isVirtualTryOnRoute && !isTryOnRoute;

  return (
    <div className="min-h-screen flex flex-col">
      {showNavbarFooter && <Navbar />}
      <main className="flex-grow">
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/why-viatryon" element={<WhyViaTryon />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/schedule-demo" element={<DemoForm />} />
            <Route path="/search" element={<SearchResults />} />
            <Route path="/virtual-try-on" element={<ProductShowcase />} />
            <Route path="/try-on/:category" element={<VirtualTryOn />} />
            <Route path="/try-on/:category/:productId" element={<VirtualTryOn />} />
            <Route path="/requirements" element={<Requirements />} />
            <Route path="/:category/:id" element={<ProductDetails />} />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/clients"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <Clients />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/analytics/*"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <AdminAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/tryon-analytics"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <TryOnAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/requests"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <Requests />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/settings"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <Settings />
                </ProtectedRoute>
              }
            />

            {/* Client Routes */}
            <Route
              path="/client/dashboard"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <ClientDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/client/analytics/*"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <ClientAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/client/settings"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <ClientSettings />
                </ProtectedRoute>
              }
            />
          </Routes>
        </main>
        {showNavbarFooter}
      </div>
  );
};

function App() {
  return (
    <Router>
      <CartProvider>
        <AppContent />
      </CartProvider>
    </Router>
  );
}

export default App;


